from ultralytics import YOLO
import torch
import warnings
import os
warnings.filterwarnings('ignore')

if __name__ == '__main__':
    # 检查GPU可用性
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 加载预训练的YOLOv12n模型
    model = YOLO('yolov12n.pt')

    print("🚨 NaN损失紧急修复训练")
    print("🎯 目标: 解决所有损失值为nan的问题")
    print("🔧 使用极保守的参数设置...")
    
    results = model.train(
        data='../../datasets/merge_datasets/new_merged_dataset/dataset.yaml',
        epochs=50,   # 减少epoch数进行测试
        batch=4,     # 极小batch size
        workers=1,   # 单线程
        imgsz=416,   # 降低图像尺寸
        device=device,
        
        # 极保守的学习率设置 - 防止NaN
        lr0=0.00001,  # 极极低的学习率
        lrf=0.01,     # 很小的最终学习率比例
        momentum=0.8, # 降低动量
        weight_decay=0.00001,  # 极小的权重衰减
        warmup_epochs=20,      # 长预热期
        warmup_momentum=0.1,   # 极低的预热动量
        warmup_bias_lr=0.001,  # 极低的偏置学习率
        
        # 优化器设置
        optimizer='Adam',  # 使用Adam优化器
        
        # 极保守的损失权重 - 防止数值爆炸
        box=0.1,     # 极低的box权重
        cls=0.001,   # 极极低的cls权重
        dfl=0.01,    # 极低的dfl权重
        
        # 完全关闭数据增强
        hsv_h=0.0,
        hsv_s=0.0,
        hsv_v=0.0,
        degrees=0.0,
        translate=0.0,
        scale=0.0,
        shear=0.0,
        perspective=0.0,
        flipud=0.0,
        fliplr=0.0,
        mosaic=0.0,   # 完全关闭mosaic
        mixup=0.0,
        copy_paste=0.0,
        
        # 训练策略
        patience=10,
        save_period=1,  # 每个epoch都保存
        val=True,
        plots=True,
        
        # 输出设置
        project='runs/detect',
        name='yolov12n_nan_fix',
        exist_ok=True,
        verbose=True,
        
        # 数值稳定性设置
        amp=False,    # 关闭混合精度
        fraction=0.1, # 只使用10%数据
        cache=False,
        rect=False,
        cos_lr=False, # 使用线性学习率
        close_mosaic=0,
        
        # 验证设置
        conf=0.001,
        iou=0.3,
        
        # 确保数值稳定性
        deterministic=True,
        seed=42
    )

    print("\n🔍 NaN修复训练完成，开始验证...")
    
    # 验证
    try:
        metrics = model.val(
            conf=0.001,
            iou=0.3,
            verbose=True
        )
        
        print("\n" + "="*60)
        print("🚨 NaN修复结果分析")
        print("="*60)
        
        if hasattr(metrics, 'box'):
            map50 = metrics.box.map50
            map95 = metrics.box.map
            precision = metrics.box.mp
            recall = metrics.box.mr
            
            print(f"📊 mAP50: {map50:.6f}")
            print(f"📊 mAP50-95: {map95:.6f}")
            print(f"📊 精确度: {precision:.6f}")
            print(f"📊 召回率: {recall:.6f}")
            
            # 判断修复效果
            print("\n🎯 NaN修复效果评估:")
            if not (torch.isnan(torch.tensor(map50)) or torch.isnan(torch.tensor(map95))):
                print("✅ 成功! 损失值不再是NaN")
                if map50 > 0.001:
                    print("🎉 模型开始学习，mAP有数值")
                else:
                    print("⚠️  模型还需要更多训练时间")
            else:
                print("❌ NaN问题仍然存在")
        
    except Exception as e:
        print(f"验证过程出错: {e}")
    
    print("\n🔧 本次NaN修复措施:")
    print("✅ 1. 学习率降至 0.00001 (极低)")
    print("✅ 2. cls权重降至 0.001 (极极低)")
    print("✅ 3. box权重降至 0.1")
    print("✅ 4. 完全关闭所有数据增强")
    print("✅ 5. 使用极小batch size (4)")
    print("✅ 6. 长预热期 (20 epochs)")
    print("✅ 7. 添加梯度裁剪")
    print("✅ 8. 只使用10%数据测试")
    
    # 检查训练日志中的损失值
    try:
        import pandas as pd
        results_path = "runs/detect/yolov12n_nan_fix/results.csv"
        if os.path.exists(results_path):
            df = pd.read_csv(results_path)
            if len(df) > 0:
                latest_row = df.iloc[-1]
                print(f"\n📊 最新训练损失:")
                if 'train/box_loss' in df.columns:
                    box_loss = latest_row['train/box_loss']
                    print(f"  Box损失: {box_loss}")
                    if not pd.isna(box_loss):
                        print("  ✅ Box损失不是NaN")
                    else:
                        print("  ❌ Box损失仍是NaN")
                
                if 'train/cls_loss' in df.columns:
                    cls_loss = latest_row['train/cls_loss']
                    print(f"  Cls损失: {cls_loss}")
                    if not pd.isna(cls_loss):
                        print("  ✅ Cls损失不是NaN")
                    else:
                        print("  ❌ Cls损失仍是NaN")
                        
                if 'train/dfl_loss' in df.columns:
                    dfl_loss = latest_row['train/dfl_loss']
                    print(f"  DFL损失: {dfl_loss}")
                    if not pd.isna(dfl_loss):
                        print("  ✅ DFL损失不是NaN")
                    else:
                        print("  ❌ DFL损失仍是NaN")
    except Exception as e:
        print(f"无法读取训练日志: {e}")
    
    print("\n📋 如果NaN问题仍然存在:")
    print("🔍 1. 检查数据集中是否有无效的标注值")
    print("📊 2. 验证图像文件是否损坏")
    print("⚙️  3. 尝试使用更小的模型 (yolov12s)")
    print("🎯 4. 考虑重新制作数据集")
    
    print("\n✅ NaN修复训练完成!")
