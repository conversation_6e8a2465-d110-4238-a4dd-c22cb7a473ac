#!/usr/bin/env python3
"""
训练问题诊断脚本
用于检查和诊断YOLOv12训练过程中的数值问题
"""

import os
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

def analyze_training_logs(log_dir="runs/detect"):
    """分析训练日志，识别数值问题"""
    print("=== 训练日志分析 ===")
    
    log_path = Path(log_dir)
    if not log_path.exists():
        print(f"❌ 日志目录不存在: {log_dir}")
        return
    
    # 查找所有训练结果
    result_files = list(log_path.glob("*/results.csv"))
    
    if not result_files:
        print("❌ 未找到训练结果文件")
        return
    
    print(f"📊 找到 {len(result_files)} 个训练结果文件")
    
    for result_file in result_files:
        print(f"\n📁 分析: {result_file.parent.name}")
        
        try:
            df = pd.read_csv(result_file)
            if len(df) < 2:
                print("⚠️  训练数据不足，跳过分析")
                continue
                
            # 分析损失值
            analyze_losses(df, result_file.parent.name)
            
            # 分析学习率
            analyze_learning_rate(df)
            
            # 分析性能指标
            analyze_metrics(df)
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")

def analyze_losses(df, experiment_name):
    """分析损失函数数值"""
    print("\n📈 损失函数分析:")
    
    # 检查训练损失
    if 'train/cls_loss' in df.columns:
        cls_loss = df['train/cls_loss'].iloc[-1]
        print(f"  分类损失: {cls_loss:.4f}")
        if cls_loss > 4.0:
            print("  ❌ 分类损失过高! 建议:")
            print("     - 降低cls权重 (建议 < 0.5)")
            print("     - 检查类别数量设置")
            print("     - 验证数据集标注质量")
    
    if 'train/box_loss' in df.columns:
        box_loss = df['train/box_loss'].iloc[-1]
        print(f"  边界框损失: {box_loss:.4f}")
        if box_loss > 3.0:
            print("  ⚠️  边界框损失较高，建议:")
            print("     - 检查标注框质量")
            print("     - 调整box权重")
    
    if 'train/dfl_loss' in df.columns:
        dfl_loss = df['train/dfl_loss'].iloc[-1]
        print(f"  DFL损失: {dfl_loss:.4f}")
        if dfl_loss > 2.5:
            print("  ⚠️  DFL损失较高，建议降低dfl权重")

def analyze_learning_rate(df):
    """分析学习率设置"""
    print("\n📊 学习率分析:")
    
    lr_cols = [col for col in df.columns if col.startswith('lr/')]
    if lr_cols:
        latest_lr = df[lr_cols[0]].iloc[-1]
        print(f"  当前学习率: {latest_lr:.6f}")
        
        if latest_lr > 0.01:
            print("  ⚠️  学习率可能过高，建议:")
            print("     - 降低初始学习率 lr0")
            print("     - 增加warmup_epochs")

def analyze_metrics(df):
    """分析性能指标"""
    print("\n🎯 性能指标分析:")
    
    if 'metrics/mAP50(B)' in df.columns:
        map50 = df['metrics/mAP50(B)'].iloc[-1]
        print(f"  mAP50: {map50:.4f}")
        
        if map50 < 0.01:
            print("  ❌ mAP50过低，可能问题:")
            print("     - 数据集标注错误")
            print("     - 类别映射问题")
            print("     - 模型配置错误")
    
    if 'metrics/precision(B)' in df.columns:
        precision = df['metrics/precision(B)'].iloc[-1]
        print(f"  精确度: {precision:.4f}")
    
    if 'metrics/recall(B)' in df.columns:
        recall = df['metrics/recall(B)'].iloc[-1]
        print(f"  召回率: {recall:.4f}")

def check_dataset_config():
    """检查数据集配置"""
    print("\n=== 数据集配置检查 ===")
    
    dataset_path = "../../datasets/merge_datasets/new_merged_dataset/dataset.yaml"
    if not os.path.exists(dataset_path):
        print(f"❌ 数据集配置文件不存在: {dataset_path}")
        print("建议:")
        print("  - 检查数据集路径是否正确")
        print("  - 确认dataset.yaml文件存在")
        return False
    
    print(f"✅ 数据集配置文件存在: {dataset_path}")
    return True

def suggest_fixes():
    """提供修复建议"""
    print("\n=== 修复建议总结 ===")
    print("基于分析结果，建议的修复措施:")
    print("\n1. 损失权重调整:")
    print("   - box: 5.0 → 3.0")
    print("   - cls: 0.2 → 0.1")
    print("   - dfl: 1.0 → 0.8")
    
    print("\n2. 学习率优化:")
    print("   - lr0: 0.0005 → 0.0003")
    print("   - warmup_epochs: 5 → 8")
    print("   - 使用余弦学习率调度")
    
    print("\n3. 数据增强:")
    print("   - 进一步减少增强强度")
    print("   - mosaic: 0.2 → 0.1")
    print("   - 关闭所有几何变换")
    
    print("\n4. 训练策略:")
    print("   - 增加batch size (如果GPU内存允许)")
    print("   - 使用梯度累积")
    print("   - 添加梯度裁剪")

def create_fixed_config():
    """创建修复后的配置"""
    print("\n=== 生成修复配置 ===")
    
    config = """
# 修复后的训练配置
# 针对数值问题的优化版本

# 核心参数
epochs: 200
batch: 16
imgsz: 640
device: cuda

# 学习率 - 大幅降低
lr0: 0.0003      # 进一步降低初始学习率
lrf: 0.1         # 提高最终学习率比例
warmup_epochs: 8 # 增加预热轮数

# 损失权重 - 重新平衡
box: 3.0         # 降低box权重
cls: 0.1         # 大幅降低cls权重
dfl: 0.8         # 降低dfl权重

# 数据增强 - 最小化
hsv_h: 0.002
hsv_s: 0.1
hsv_v: 0.02
degrees: 0.0
translate: 0.005
scale: 0.02
mosaic: 0.1
mixup: 0.0

# 训练策略
patience: 100
optimizer: AdamW
weight_decay: 0.0001
cos_lr: true
"""
    
    with open("fixed_config.yaml", "w", encoding="utf-8") as f:
        f.write(config)
    
    print("✅ 修复配置已保存到 fixed_config.yaml")

if __name__ == "__main__":
    print("🔍 YOLOv12 训练问题诊断工具")
    print("=" * 50)
    
    # 分析训练日志
    analyze_training_logs()
    
    # 检查数据集
    check_dataset_config()
    
    # 提供修复建议
    suggest_fixes()
    
    # 创建修复配置
    create_fixed_config()
    
    print("\n" + "=" * 50)
    print("🎯 诊断完成! 请根据建议调整训练参数")
