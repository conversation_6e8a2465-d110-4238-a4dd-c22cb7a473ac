
# 修复后的训练配置
# 针对数值问题的优化版本

# 核心参数
epochs: 200
batch: 16
imgsz: 640
device: cuda

# 学习率 - 大幅降低
lr0: 0.0003      # 进一步降低初始学习率
lrf: 0.1         # 提高最终学习率比例
warmup_epochs: 8 # 增加预热轮数

# 损失权重 - 重新平衡
box: 3.0         # 降低box权重
cls: 0.1         # 大幅降低cls权重
dfl: 0.8         # 降低dfl权重

# 数据增强 - 最小化
hsv_h: 0.002
hsv_s: 0.1
hsv_v: 0.02
degrees: 0.0
translate: 0.005
scale: 0.02
mosaic: 0.1
mixup: 0.0

# 训练策略
patience: 100
optimizer: AdamW
weight_decay: 0.0001
cos_lr: true
