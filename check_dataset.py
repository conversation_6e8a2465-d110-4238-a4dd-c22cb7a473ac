#!/usr/bin/env python3
"""
数据集检查脚本 - 诊断数据集配置问题
"""

import os
import yaml
from pathlib import Path

def check_dataset_config():
    """检查数据集配置文件"""
    print("🔍 检查数据集配置...")
    
    dataset_paths = [
        "../../datasets/merge_datasets/new_merged_dataset/dataset.yaml",
        "../datasets/merge_datasets/new_merged_dataset/dataset.yaml",
        "datasets/merge_datasets/new_merged_dataset/dataset.yaml",
        "dataset.yaml"
    ]
    
    dataset_file = None
    for path in dataset_paths:
        if os.path.exists(path):
            dataset_file = path
            break
    
    if not dataset_file:
        print("❌ 未找到数据集配置文件!")
        print("请检查以下路径:")
        for path in dataset_paths:
            print(f"  - {path}")
        return False
    
    print(f"✅ 找到数据集配置: {dataset_file}")
    
    try:
        with open(dataset_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print("\n📊 数据集配置内容:")
        print(f"  类别数量: {config.get('nc', '未知')}")
        print(f"  类别名称: {config.get('names', '未知')}")
        print(f"  训练集: {config.get('train', '未知')}")
        print(f"  验证集: {config.get('val', '未知')}")
        
        # 检查类别数量
        nc = config.get('nc')
        names = config.get('names')
        
        if nc is None:
            print("❌ 缺少类别数量配置 (nc)")
            return False
        
        if names is None:
            print("❌ 缺少类别名称配置 (names)")
            return False
        
        if len(names) != nc:
            print(f"❌ 类别数量不匹配! nc={nc}, len(names)={len(names)}")
            return False
        
        print("✅ 数据集配置基本正确")
        return True, config
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def check_dataset_structure(config):
    """检查数据集文件结构"""
    print("\n🔍 检查数据集文件结构...")
    
    train_path = config.get('train')
    val_path = config.get('val')
    
    if not train_path or not val_path:
        print("❌ 训练集或验证集路径未配置")
        return False
    
    # 检查路径是否存在
    base_dir = os.path.dirname("../../datasets/merge_datasets/new_merged_dataset/dataset.yaml")
    
    train_full_path = os.path.join(base_dir, train_path) if not os.path.isabs(train_path) else train_path
    val_full_path = os.path.join(base_dir, val_path) if not os.path.isabs(val_path) else val_path
    
    print(f"  训练集路径: {train_full_path}")
    print(f"  验证集路径: {val_full_path}")
    
    if not os.path.exists(train_full_path):
        print(f"❌ 训练集路径不存在: {train_full_path}")
        return False
    
    if not os.path.exists(val_full_path):
        print(f"❌ 验证集路径不存在: {val_full_path}")
        return False
    
    print("✅ 数据集路径存在")
    return True

def create_minimal_test_config():
    """创建最小测试配置"""
    print("\n🛠️ 创建最小测试配置...")
    
    # 创建一个最小的测试配置
    test_config = {
        'nc': 2,  # 假设是人脸和车牌两个类别
        'names': ['face', 'license_plate'],
        'train': '../../datasets/merge_datasets/new_merged_dataset/train',
        'val': '../../datasets/merge_datasets/new_merged_dataset/val'
    }
    
    with open('test_dataset.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(test_config, f, default_flow_style=False, allow_unicode=True)
    
    print("✅ 测试配置已保存到 test_dataset.yaml")
    return 'test_dataset.yaml'

def suggest_dataset_fixes():
    """建议数据集修复方案"""
    print("\n🔧 数据集问题修复建议:")
    print("="*50)
    
    print("1. 检查类别配置:")
    print("   - 确认nc (类别数量) 正确")
    print("   - 确认names列表长度与nc一致")
    print("   - 检查类别名称是否正确")
    
    print("\n2. 检查标注文件:")
    print("   - 标注文件中的类别ID应该从0开始")
    print("   - 类别ID不能超过 nc-1")
    print("   - 边界框坐标应该是归一化的 (0-1)")
    
    print("\n3. 检查数据集路径:")
    print("   - 确认train和val路径正确")
    print("   - 确认images和labels文件夹存在")
    print("   - 确认图片和标注文件一一对应")
    
    print("\n4. 常见问题修复:")
    print("   - 如果是中文路径，改为英文路径")
    print("   - 检查文件权限")
    print("   - 确认没有空的标注文件")

if __name__ == "__main__":
    print("🔍 数据集诊断工具")
    print("="*50)
    
    # 检查数据集配置
    result = check_dataset_config()
    
    if result and len(result) == 2:
        success, config = result
        if success:
            # 检查数据集结构
            check_dataset_structure(config)
    
    # 创建测试配置
    test_config_path = create_minimal_test_config()
    
    # 提供修复建议
    suggest_dataset_fixes()
    
    print("\n" + "="*50)
    print("🎯 建议下一步操作:")
    print("1. 先使用 test_dataset.yaml 进行测试训练")
    print("2. 如果仍有问题，检查标注文件格式")
    print("3. 考虑使用更简单的数据集进行测试")
