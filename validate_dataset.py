#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集验证脚本
检查YOLO数据集的加载和标签是否正确
"""

import os
import yaml
import cv2
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
from collections import Counter
import random

def load_dataset_config(yaml_path):
    """加载数据集配置"""
    with open(yaml_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def validate_labels(labels_dir, max_samples=1000):
    """验证标签文件"""
    print(f"验证标签目录: {labels_dir}")
    
    label_files = list(Path(labels_dir).glob("*.txt"))
    print(f"找到 {len(label_files)} 个标签文件")
    
    if len(label_files) == 0:
        print("错误: 没有找到标签文件")
        return False
    
    # 随机采样验证
    sample_files = random.sample(label_files, min(max_samples, len(label_files)))
    
    class_counts = Counter()
    total_objects = 0
    invalid_files = []
    
    for label_file in sample_files:
        try:
            with open(label_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue
                
                parts = line.split()
                if len(parts) != 5:
                    print(f"警告: {label_file} 第{line_num}行格式错误: {line}")
                    invalid_files.append(str(label_file))
                    continue
                
                try:
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    # 检查坐标范围
                    if not (0 <= x_center <= 1 and 0 <= y_center <= 1 and 
                           0 <= width <= 1 and 0 <= height <= 1):
                        print(f"警告: {label_file} 第{line_num}行坐标超出范围: {line}")
                        invalid_files.append(str(label_file))
                        continue
                    
                    class_counts[class_id] += 1
                    total_objects += 1
                    
                except ValueError as e:
                    print(f"警告: {label_file} 第{line_num}行数据格式错误: {line} - {e}")
                    invalid_files.append(str(label_file))
                    continue
                    
        except Exception as e:
            print(f"错误: 无法读取文件 {label_file}: {e}")
            invalid_files.append(str(label_file))
    
    print(f"\n=== 标签验证结果 ===")
    print(f"验证文件数: {len(sample_files)}")
    print(f"总目标数: {total_objects}")
    print(f"无效文件数: {len(set(invalid_files))}")
    print(f"类别分布: {dict(class_counts)}")
    
    return len(set(invalid_files)) == 0

def check_image_label_pairs(images_dir, labels_dir, max_samples=100):
    """检查图像和标签文件的对应关系"""
    print(f"\n检查图像-标签对应关系...")
    
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
        image_files.extend(Path(images_dir).glob(ext))
    
    print(f"图像文件数: {len(image_files)}")
    
    # 随机采样
    sample_images = random.sample(image_files, min(max_samples, len(image_files)))
    
    missing_labels = []
    missing_images = []
    valid_pairs = 0
    
    for img_file in sample_images:
        label_file = Path(labels_dir) / (img_file.stem + '.txt')
        
        if not label_file.exists():
            missing_labels.append(str(img_file))
        else:
            # 检查图像是否可读
            try:
                img = cv2.imread(str(img_file))
                if img is None:
                    missing_images.append(str(img_file))
                else:
                    valid_pairs += 1
            except Exception as e:
                print(f"无法读取图像 {img_file}: {e}")
                missing_images.append(str(img_file))
    
    print(f"有效图像-标签对: {valid_pairs}")
    print(f"缺少标签的图像: {len(missing_labels)}")
    print(f"无法读取的图像: {len(missing_images)}")
    
    if missing_labels:
        print(f"缺少标签的图像示例: {missing_labels[:5]}")
    if missing_images:
        print(f"无法读取的图像示例: {missing_images[:5]}")
    
    return len(missing_labels) == 0 and len(missing_images) == 0

def visualize_samples(images_dir, labels_dir, class_names, num_samples=5):
    """可视化一些样本"""
    print(f"\n可视化 {num_samples} 个样本...")
    
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
        image_files.extend(Path(images_dir).glob(ext))
    
    if len(image_files) == 0:
        print("没有找到图像文件")
        return
    
    sample_images = random.sample(image_files, min(num_samples, len(image_files)))
    
    fig, axes = plt.subplots(1, len(sample_images), figsize=(15, 3))
    if len(sample_images) == 1:
        axes = [axes]
    
    for i, img_file in enumerate(sample_images):
        # 读取图像
        img = cv2.imread(str(img_file))
        if img is None:
            continue
        
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        h, w = img.shape[:2]
        
        # 读取标签
        label_file = Path(labels_dir) / (img_file.stem + '.txt')
        if label_file.exists():
            with open(label_file, 'r') as f:
                lines = f.readlines()
            
            # 绘制边界框
            for line in lines:
                parts = line.strip().split()
                if len(parts) == 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1]) * w
                    y_center = float(parts[2]) * h
                    width = float(parts[3]) * w
                    height = float(parts[4]) * h
                    
                    x1 = int(x_center - width/2)
                    y1 = int(y_center - height/2)
                    x2 = int(x_center + width/2)
                    y2 = int(y_center + height/2)
                    
                    # 绘制矩形
                    cv2.rectangle(img, (x1, y1), (x2, y2), (255, 0, 0), 2)
                    
                    # 添加类别标签
                    class_name = class_names.get(class_id, f"class_{class_id}")
                    cv2.putText(img, class_name, (x1, y1-10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
        
        axes[i].imshow(img)
        axes[i].set_title(f"{img_file.name}")
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.savefig('dataset_samples.png', dpi=150, bbox_inches='tight')
    plt.show()
    print("样本可视化已保存为 dataset_samples.png")

def main():
    # 数据集配置
    dataset_yaml = "d:/projects/01_face_car_Information_Desensitization/programs/datasets/merge_datasets/new_merged_dataset/dataset.yaml"
    
    print("=== YOLO数据集验证 ===")
    
    # 加载配置
    try:
        config = load_dataset_config(dataset_yaml)
        print(f"数据集路径: {config['path']}")
        print(f"类别数: {config['nc']}")
        print(f"类别名称: {config['names']}")
    except Exception as e:
        print(f"无法加载数据集配置: {e}")
        return
    
    # 构建路径
    dataset_root = Path(config['path'])
    train_images_dir = dataset_root / config['train']
    train_labels_dir = dataset_root / "labels" / "train"
    
    print(f"\n训练图像目录: {train_images_dir}")
    print(f"训练标签目录: {train_labels_dir}")
    
    # 检查目录是否存在
    if not train_images_dir.exists():
        print(f"错误: 训练图像目录不存在 {train_images_dir}")
        return
    
    if not train_labels_dir.exists():
        print(f"错误: 训练标签目录不存在 {train_labels_dir}")
        return
    
    # 验证标签
    labels_valid = validate_labels(train_labels_dir, max_samples=1000)
    
    # 检查图像-标签对应关系
    pairs_valid = check_image_label_pairs(train_images_dir, train_labels_dir, max_samples=100)
    
    # 可视化样本
    try:
        visualize_samples(train_images_dir, train_labels_dir, config['names'], num_samples=3)
    except Exception as e:
        print(f"可视化失败: {e}")
    
    # 总结
    print(f"\n=== 验证总结 ===")
    print(f"标签格式: {'✓ 正确' if labels_valid else '✗ 有问题'}")
    print(f"图像-标签对应: {'✓ 正确' if pairs_valid else '✗ 有问题'}")
    
    if labels_valid and pairs_valid:
        print("\n✅ 数据集验证通过！")
    else:
        print("\n❌ 数据集存在问题，请检查上述错误信息")

if __name__ == "__main__":
    main()