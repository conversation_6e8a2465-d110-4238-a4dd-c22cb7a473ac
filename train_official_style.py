from ultralytics import YOLO

# 参考官方案例的训练脚本
if __name__ == '__main__':
    print("🚀 官方风格训练 - 基于YOLO官方案例")
    
    # 使用YAML配置文件创建模型（如官方案例）
    model = YOLO('yolov12n.yaml')
    
    # 参考官方案例的参数，但适配我们的数据集
    results = model.train(
        data='../../datasets/merge_datasets/new_merged_dataset/dataset.yaml',
        epochs=50,     # 官方用600，我们先用50
        batch=16,      # 官方用256，我们用16
        imgsz=640,     # 保持640
        scale=0.3,     # 官方S:0.9，我们用保守的0.3
        mosaic=0.5,    # 官方1.0，我们用0.5
        mixup=0.0,     # 官方S:0.05，我们先用0.0
        copy_paste=0.0, # 官方S:0.15，我们先用0.0
        device="cuda", # 单GPU
        project='runs/detect',
        name='official_style',
        exist_ok=True,
        verbose=True
    )
    
    print("✅ 官方风格训练完成!")
    
    # 评估模型性能
    metrics = model.val()
    print(f"mAP50: {metrics.box.map50:.4f}")
    print(f"mAP50-95: {metrics.box.map:.4f}")
    
    # 测试检测（如官方案例）
    try:
        # 找一张测试图片
        import os
        test_dir = "../../datasets/merge_datasets/new_merged_dataset/images/train"
        if os.path.exists(test_dir):
            images = [f for f in os.listdir(test_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            if images:
                test_image = os.path.join(test_dir, images[0])
                results = model(test_image)
                print(f"✅ 成功检测图片: {images[0]}")
                # results[0].show()  # 显示结果
    except Exception as e:
        print(f"检测测试失败: {e}")
    
    print("🎯 训练总结:")
    print("- 使用YAML配置文件初始化模型")
    print("- 参考官方案例参数")
    print("- 适配我们的数据集大小")
    print("- 保守的数据增强设置")
