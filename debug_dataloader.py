import torch
import yaml
from ultralytics import <PERSON>OL<PERSON>
from ultralytics.data import YOLODataset
from ultralytics.data.utils import check_det_dataset
import os
from pathlib import Path

def debug_dataloader():
    print("=== 调试数据加载器 ===")
    
    # 加载数据集配置
    data_path = "../../datasets/merge_datasets/new_merged_dataset/dataset.yaml"
    with open(data_path, 'r', encoding='utf-8') as f:
        data_config = yaml.safe_load(f)
    
    print(f"数据集配置: {data_config}")
    
    # 检查数据集
    try:
        data_dict = check_det_dataset(data_path)
        print(f"\n检查后的数据集配置: {data_dict}")
    except Exception as e:
        print(f"数据集检查失败: {e}")
        return
    
    # 创建数据集对象
    try:
        # 创建默认的hyp参数
        from ultralytics.cfg import get_cfg
        from ultralytics.utils import DEFAULT_CFG
        
        hyp = get_cfg(DEFAULT_CFG)
        
        dataset = YOLODataset(
            img_path=data_dict['train'],
            imgsz=640,
            cache=False,
            augment=False,
            hyp=hyp,
            prefix='train: ',
            rect=False,
            batch_size=16
        )
        
        print(f"\n数据集信息:")
        print(f"图像数量: {len(dataset)}")
        print(f"标签文件路径: {dataset.label_files[:5]}...")  # 显示前5个
        
        # 检查几个样本
        print("\n检查前几个样本:")
        for i in range(min(5, len(dataset))):
            try:
                sample = dataset[i]
                if 'bboxes' in sample:
                    print(f"样本 {i}: 图像形状={sample['img'].shape}, 边界框数量={len(sample['bboxes'])}")
                    if len(sample['bboxes']) > 0:
                        print(f"  边界框: {sample['bboxes'][:3]}...")  # 显示前3个
                        print(f"  类别: {sample['cls'][:3]}...")  # 显示前3个
                else:
                    print(f"样本 {i}: 没有边界框数据")
            except Exception as e:
                print(f"样本 {i} 加载失败: {e}")
        
        # 创建数据加载器
        from torch.utils.data import DataLoader
        dataloader = DataLoader(
            dataset,
            batch_size=16,
            shuffle=False,
            num_workers=0,  # 设为0避免多进程问题
            collate_fn=getattr(dataset, 'collate_fn', None)
        )
        
        print(f"\n数据加载器信息:")
        print(f"批次数量: {len(dataloader)}")
        
        # 检查第一个批次
        try:
            batch = next(iter(dataloader))
            print(f"第一个批次:")
            if isinstance(batch, dict):
                for key, value in batch.items():
                    if torch.is_tensor(value):
                        print(f"  {key}: {value.shape}")
                    else:
                        print(f"  {key}: {type(value)}")
            else:
                print(f"  批次类型: {type(batch)}")
                if hasattr(batch, '__len__'):
                    print(f"  批次长度: {len(batch)}")
        except Exception as e:
            print(f"加载第一个批次失败: {e}")
            
    except Exception as e:
        print(f"创建数据集失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_dataloader()