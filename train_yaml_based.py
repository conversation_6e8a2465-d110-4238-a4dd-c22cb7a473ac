from ultralytics import YOLO
import torch
import warnings
warnings.filterwarnings('ignore')

if __name__ == '__main__':
    # 检查GPU可用性
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 参考官方案例，使用YAML配置文件加载模型
    model = YOLO('yolov12n.yaml')  # 使用YAML配置而不是预训练权重

    print("🔧 基于YAML配置的训练 - 参考官方案例")
    print("🎯 目标: 使用官方推荐的参数解决NaN问题")
    
    # 参考官方案例的训练参数，但调整为适合我们数据集的保守设置
    results = model.train(
        data='../../datasets/merge_datasets/new_merged_dataset/dataset.yaml',
        epochs=100,   # 参考案例使用600，我们先用100测试
        batch=32,     # 参考案例使用256，我们用32
        imgsz=640,    # 保持640
        device=device,
        
        # 参考案例的数据增强参数，但更保守
        scale=0.3,    # 参考案例S:0.9，我们用更保守的0.3
        mosaic=0.5,   # 参考案例1.0，我们用0.5
        mixup=0.0,    # 参考案例S:0.05，我们先用0.0
        copy_paste=0.0, # 参考案例S:0.15，我们先用0.0
        
        # 学习率使用默认值，让ultralytics自动设置
        # lr0, lrf等参数使用默认值
        
        # 损失权重使用默认值，让模型自动平衡
        # box, cls, dfl使用默认值
        
        # 训练策略
        patience=50,   # 增加耐心值
        save_period=5, # 每5个epoch保存
        val=True,
        plots=True,
        
        # 输出设置
        project='runs/detect',
        name='yolov12n_yaml_based',
        exist_ok=True,
        verbose=True,
        
        # 稳定性设置
        amp=True,     # 参考案例通常开启AMP
        cache=False,
        rect=False,
        cos_lr=True,  # 使用余弦学习率
        
        # 验证设置
        conf=0.001,
        iou=0.6,
        
        # 确保可重现性
        deterministic=True,
        seed=42,
    )

    print("\n🔍 YAML基础训练完成，开始验证...")
    
    # 验证模型性能
    metrics = model.val()
    
    print("\n" + "="*60)
    print("🔧 YAML基础训练结果分析")
    print("="*60)
    
    if hasattr(metrics, 'box'):
        map50 = metrics.box.map50
        map95 = metrics.box.map
        precision = metrics.box.mp
        recall = metrics.box.mr
        
        print(f"📊 mAP50: {map50:.6f}")
        print(f"📊 mAP50-95: {map95:.6f}")
        print(f"📊 精确度: {precision:.6f}")
        print(f"📊 召回率: {recall:.6f}")
        
        # 判断训练效果
        print("\n🎯 训练效果评估:")
        if not (torch.isnan(torch.tensor(map50)) or torch.isnan(torch.tensor(map95))):
            print("✅ 成功! 没有NaN问题")
            if map50 > 0.1:
                print("🎉 优秀! mAP50 > 0.1，模型学习良好")
            elif map50 > 0.01:
                print("📈 良好! mAP50 > 0.01，模型开始学习")
            else:
                print("⚠️  需要更多训练时间或参数调整")
        else:
            print("❌ 仍有NaN问题，需要进一步调试")
    
    # 执行目标检测测试
    print("\n🔍 执行目标检测测试...")
    try:
        # 如果有测试图像，可以进行检测
        test_image_path = "../../datasets/merge_datasets/new_merged_dataset/images/train"
        import os
        if os.path.exists(test_image_path):
            # 获取第一张图像进行测试
            image_files = [f for f in os.listdir(test_image_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            if image_files:
                test_image = os.path.join(test_image_path, image_files[0])
                results = model(test_image)
                print(f"✅ 成功对图像 {image_files[0]} 进行检测")
                # results[0].show()  # 注释掉显示，避免GUI问题
            else:
                print("⚠️  未找到测试图像")
        else:
            print("⚠️  测试图像路径不存在")
    except Exception as e:
        print(f"目标检测测试失败: {e}")
    
    print("\n🔧 本次训练特点:")
    print("✅ 1. 使用YAML配置文件 (yolov12n.yaml)")
    print("✅ 2. 参考官方案例参数")
    print("✅ 3. 保守的数据增强设置")
    print("✅ 4. 使用默认损失权重")
    print("✅ 5. 启用混合精度训练")
    print("✅ 6. 余弦学习率调度")
    
    print("\n📋 与官方案例的对比:")
    print("🔄 官方: epochs=600, batch=256, scale=0.5")
    print("🔄 我们: epochs=100, batch=32, scale=0.3")
    print("🔄 原因: 适应我们的数据集大小和硬件配置")
    
    print("\n✅ YAML基础训练完成!")
    print("💡 如果效果良好，可以逐步增加epochs和batch size")
