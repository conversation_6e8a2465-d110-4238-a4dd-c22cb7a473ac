from ultralytics import YOLO
import torch

if __name__ == '__main__':
    print("🔧 最小化测试训练 - 解决NaN问题")
    
    # 使用CUDA
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"设备: {device}")
    
    # 加载模型
    model = YOLO('yolov12n.pt')
    
    # 最简化训练参数
    results = model.train(
        data='../../datasets/merge_datasets/new_merged_dataset/dataset.yaml',
        epochs=5,      # 只训练5个epoch测试
        batch=1,       # 最小batch
        imgsz=320,     # 小尺寸
        lr0=0.000001,  # 极低学习率
        box=0.1,       # 低权重
        cls=0.01,      # 低分类权重
        dfl=0.1,       # 低DFL权重
        mosaic=0.0,    # 关闭增强
        amp=False,     # 关闭混合精度
        workers=1,     # 单线程
        project='runs/detect',
        name='minimal_test',
        exist_ok=True,
        verbose=True
    )
    
    print("✅ 最小化测试完成!")
    
    # 检查结果
    try:
        metrics = model.val()
        print(f"mAP50: {metrics.box.map50}")
        if not torch.isnan(torch.tensor(metrics.box.map50)):
            print("🎉 成功! 没有NaN问题")
        else:
            print("❌ 仍有NaN问题")
    except Exception as e:
        print(f"验证失败: {e}")
