from ultralytics import YOLO
import torch

if __name__ == '__main__':
    # 检查GPU可用性
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 加载预训练的YOLOv12n模型
    model = YOLO('yolov12n.pt')

    # 优化的训练参数 - 修复版本
    results = model.train(
        data='../../datasets/merge_datasets/new_merged_dataset/dataset.yaml',
        epochs=200,
        batch=16,
        workers=4,
        imgsz=640,
        device=device,
        
        # 学习率调度优化 - 降低学习率
        lr0=0.001,    # 大幅降低初始学习率
        lrf=0.01,     # 调整最终学习率因子
        momentum=0.937,
        weight_decay=0.0005,
        warmup_epochs=3,  # 减少预热轮数
        warmup_momentum=0.8,
        warmup_bias_lr=0.1,
        
        # 优化器设置
        optimizer='AdamW',
        
        # 损失函数权重调整 - 平衡权重
        box=7.5,     # 保持边界框损失权重
        cls=0.5,     # 大幅降低分类损失权重
        dfl=1.5,     # 保持分布焦点损失权重
        
        # 数据增强进一步优化
        hsv_h=0.01,   # 进一步减少色调增强
        hsv_s=0.3,    # 减少饱和度增强
        hsv_v=0.1,    # 减少明度增强
        degrees=0.0,  # 关闭旋转
        translate=0.02, # 进一步减少平移
        scale=0.1,    # 进一步减少缩放
        shear=0.0,    # 关闭剪切
        perspective=0.0, # 关闭透视变换
        flipud=0.0,   # 关闭上下翻转
        fliplr=0.3,   # 减少左右翻转概率
        mosaic=0.3,   # 进一步减少马赛克增强
        mixup=0.0,    # 关闭mixup
        copy_paste=0.0, # 关闭复制粘贴
        
        # 训练策略
        patience=30,  # 减少早停耐心值
        save_period=10, # 每10个epoch保存一次
        val=True,
        plots=True,
        
        # 输出设置
        project='runs/detect',
        name='yolov12n_face_plate_fixed',
        exist_ok=True,
        verbose=True,
        
        # 高级设置
        amp=True,
        fraction=1.0,
        cache=False,
        rect=False,
        cos_lr=True,
        close_mosaic=15, # 提前关闭mosaic
        
        # 验证设置优化
        conf=0.001,   # 降低置信度阈值
        iou=0.6,      # 调整IoU阈值
    )

    # 在验证集上评估模型性能
    print("\n开始模型验证...")
    metrics = model.val(
        conf=0.001,   # 使用低置信度阈值进行验证
        iou=0.6,      # 使用调整后的IoU阈值
        verbose=True
    )
    
    # 输出训练结果
    print("\n=== 训练完成 ===")
    print(f"最佳模型保存在: {results.save_dir}")
    print(f"最佳mAP50: {metrics.box.map50:.4f}")
    print(f"最佳mAP50-95: {metrics.box.map:.4f}")
    
    # 保存最终模型
    model.export(format='onnx')
    print("模型已导出为ONNX格式")