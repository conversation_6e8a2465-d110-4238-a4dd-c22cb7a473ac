from ultralytics import YOLO
import torch

if __name__ == '__main__':
    # 检查GPU可用性
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")

    # 加载预训练的YOLOv12n模型
    model = YOLO('yolov12n.pt')

    # 数值问题修复版本 - 重新平衡损失权重和学习率
    results = model.train(
        data='../../datasets/merge_datasets/new_merged_dataset/dataset.yaml',
        epochs=200,
        batch=16,
        workers=4,
        imgsz=640,
        device=device,

        # 学习率调度优化 - 进一步降低学习率解决数值问题
        lr0=0.0005,   # 进一步降低初始学习率，防止梯度爆炸
        lrf=0.05,     # 提高最终学习率因子，保持训练稳定性
        momentum=0.937,
        weight_decay=0.0003,  # 降低权重衰减，减少过度正则化
        warmup_epochs=5,      # 增加预热轮数，让模型更平稳地开始训练
        warmup_momentum=0.8,
        warmup_bias_lr=0.05,  # 降低偏置学习率

        # 优化器设置
        optimizer='AdamW',

        # 损失函数权重重新平衡 - 解决分类损失过高问题
        box=5.0,     # 降低边界框损失权重，避免过度关注定位
        cls=0.2,     # 大幅降低分类损失权重，解决cls_loss过高问题
        dfl=1.0,     # 降低分布焦点损失权重

        # 数据增强大幅优化 - 减少增强强度，提高训练稳定性
        hsv_h=0.005,  # 大幅减少色调增强，避免颜色失真
        hsv_s=0.2,    # 进一步减少饱和度增强
        hsv_v=0.05,   # 大幅减少明度增强
        degrees=0.0,  # 关闭旋转
        translate=0.01, # 大幅减少平移，避免目标偏移过大
        scale=0.05,   # 大幅减少缩放，保持目标尺寸稳定
        shear=0.0,    # 关闭剪切
        perspective=0.0, # 关闭透视变换
        flipud=0.0,   # 关闭上下翻转
        fliplr=0.2,   # 进一步减少左右翻转概率
        mosaic=0.2,   # 大幅减少马赛克增强，避免训练不稳定
        mixup=0.0,    # 关闭mixup
        copy_paste=0.0, # 关闭复制粘贴

        # 训练策略优化
        patience=50,  # 增加早停耐心值，给模型更多收敛时间
        save_period=5, # 每5个epoch保存一次，便于监控
        val=True,
        plots=True,

        # 输出设置
        project='runs/detect',
        name='yolov12n_face_plate_numerical_fixed',
        exist_ok=True,
        verbose=True,

        # 高级设置优化
        amp=True,     # 保持混合精度训练
        fraction=1.0,
        cache=False,
        rect=False,
        cos_lr=True,  # 使用余弦学习率调度
        close_mosaic=20, # 延后关闭mosaic，给模型更多学习时间

        # 验证设置优化
        conf=0.001,   # 保持低置信度阈值
        iou=0.6,      # 调整IoU阈值

        # 添加梯度裁剪防止梯度爆炸
        max_norm=10.0,  # 梯度裁剪阈值
    )

    # 在验证集上评估模型性能
    print("\n开始模型验证...")
    metrics = model.val(
        conf=0.001,   # 使用低置信度阈值进行验证
        iou=0.6,      # 使用调整后的IoU阈值
        verbose=True
    )

    # 输出训练结果和数值分析
    print("\n=== 训练完成 ===")
    print(f"最佳模型保存在: {results.save_dir}")
    print(f"最佳mAP50: {metrics.box.map50:.4f}")
    print(f"最佳mAP50-95: {metrics.box.map:.4f}")

    # 检查训练过程中的数值稳定性
    if hasattr(results, 'results_dict'):
        print("\n=== 数值稳定性检查 ===")
        if 'train/cls_loss' in results.results_dict:
            cls_loss = results.results_dict['train/cls_loss']
            print(f"分类损失: {cls_loss:.4f}")
            if cls_loss > 3.0:
                print("⚠️  警告: 分类损失仍然较高，建议进一步降低cls权重")
        if 'train/box_loss' in results.results_dict:
            box_loss = results.results_dict['train/box_loss']
            print(f"边界框损失: {box_loss:.4f}")

    # 保存最终模型
    model.export(format='onnx')
    print("模型已导出为ONNX格式")

    print("\n=== 修复说明 ===")
    print("1. 大幅降低了分类损失权重 (cls=0.2)")
    print("2. 降低了初始学习率 (lr0=0.0005)")
    print("3. 减少了数据增强强度")
    print("4. 增加了训练稳定性措施")
    print("5. 如果损失仍然异常，请检查数据集标注质量")