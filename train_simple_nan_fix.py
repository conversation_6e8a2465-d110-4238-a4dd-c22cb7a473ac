from ultralytics import YOLO
import torch
import warnings
warnings.filterwarnings('ignore')

if __name__ == '__main__':
    # 检查GPU可用性
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 加载预训练的YOLOv12n模型
    model = YOLO('yolov12n.pt')

    print("🚨 简单NaN修复训练")
    print("🎯 目标: 使用最简单的参数解决NaN问题")
    
    results = model.train(
        data='../../datasets/merge_datasets/new_merged_dataset/dataset.yaml',
        epochs=20,    # 短期测试
        batch=2,      # 极小batch
        imgsz=320,    # 小图像尺寸
        device=device,
        
        # 极低学习率
        lr0=0.000001,  # 比之前更低
        lrf=0.01,
        
        # 极低损失权重
        box=0.01,
        cls=0.0001,   # 极极低
        dfl=0.001,
        
        # 关闭所有增强
        mosaic=0.0,
        mixup=0.0,
        copy_paste=0.0,
        hsv_h=0.0,
        hsv_s=0.0,
        hsv_v=0.0,
        degrees=0.0,
        translate=0.0,
        scale=0.0,
        flipud=0.0,
        fliplr=0.0,
        
        # 基本设置
        amp=False,
        patience=5,
        save_period=1,
        project='runs/detect',
        name='simple_nan_fix',
        exist_ok=True,
        verbose=True
    )

    print("✅ 简单NaN修复训练完成!")
    
    # 验证
    try:
        metrics = model.val()
        print(f"mAP50: {metrics.box.map50:.6f}")
        print(f"mAP50-95: {metrics.box.map:.6f}")
    except Exception as e:
        print(f"验证失败: {e}")
