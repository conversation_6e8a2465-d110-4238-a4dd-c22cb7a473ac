task: detect
mode: train
model: yolov12n.pt
data: ../../datasets/merge_datasets/new_merged_dataset/dataset.yaml
epochs: 30
time: null
patience: 15
batch: 8
imgsz: 640
save: true
save_period: 2
cache: false
device: cuda
workers: 2
project: runs/detect
name: yolov12n_simple_fix
exist_ok: true
pretrained: true
optimizer: AdamW
verbose: true
seed: 42
deterministic: true
single_cls: false
rect: false
cos_lr: true
close_mosaic: 5
resume: false
amp: false
fraction: 0.3
profile: false
freeze: null
multi_scale: false
overlap_mask: true
mask_ratio: 4
dropout: 0.0
val: true
split: val
save_json: false
save_hybrid: false
conf: 0.01
iou: 0.5
max_det: 300
half: false
dnn: false
plots: true
source: null
vid_stride: 1
stream_buffer: false
visualize: false
augment: false
agnostic_nms: false
classes: null
retina_masks: false
embed: null
show: false
save_frames: false
save_txt: false
save_conf: false
save_crop: false
show_labels: true
show_conf: true
show_boxes: true
line_width: null
format: torchscript
keras: false
optimize: false
int8: false
dynamic: false
simplify: true
opset: null
workspace: null
nms: false
lr0: 0.0001
lrf: 0.1
momentum: 0.9
weight_decay: 0.0001
warmup_epochs: 10
warmup_momentum: 0.5
warmup_bias_lr: 0.01
box: 1.0
cls: 0.01
dfl: 0.2
pose: 12.0
kobj: 1.0
nbs: 64
hsv_h: 0.001
hsv_s: 0.01
hsv_v: 0.001
degrees: 0.0
translate: 0.001
scale: 0.01
shear: 0.0
perspective: 0.0
flipud: 0.0
fliplr: 0.1
bgr: 0.0
mosaic: 0.05
mixup: 0.0
copy_paste: 0.0
copy_paste_mode: flip
auto_augment: randaugment
erasing: 0.4
crop_fraction: 1.0
cfg: null
tracker: botsort.yaml
save_dir: runs\detect\yolov12n_simple_fix
