task: detect
mode: train
model: yolov12n.pt
data: ../../datasets/merge_datasets/new_merged_dataset/dataset.yaml
epochs: 200
time: null
patience: 30
batch: 16
imgsz: 640
save: true
save_period: 10
cache: false
device: cuda
workers: 4
project: runs/detect
name: yo<PERSON>12n_face_plate_fixed
exist_ok: true
pretrained: true
optimizer: AdamW
verbose: true
seed: 0
deterministic: true
single_cls: false
rect: false
cos_lr: true
close_mosaic: 15
resume: false
amp: true
fraction: 1.0
profile: false
freeze: null
multi_scale: false
overlap_mask: true
mask_ratio: 4
dropout: 0.0
val: true
split: val
save_json: false
save_hybrid: false
conf: 0.001
iou: 0.6
max_det: 300
half: false
dnn: false
plots: true
source: null
vid_stride: 1
stream_buffer: false
visualize: false
augment: false
agnostic_nms: false
classes: null
retina_masks: false
embed: null
show: false
save_frames: false
save_txt: false
save_conf: false
save_crop: false
show_labels: true
show_conf: true
show_boxes: true
line_width: null
format: torchscript
keras: false
optimize: false
int8: false
dynamic: false
simplify: true
opset: null
workspace: null
nms: false
lr0: 0.001
lrf: 0.01
momentum: 0.937
weight_decay: 0.0005
warmup_epochs: 3
warmup_momentum: 0.8
warmup_bias_lr: 0.1
box: 7.5
cls: 0.5
dfl: 1.5
pose: 12.0
kobj: 1.0
nbs: 64
hsv_h: 0.01
hsv_s: 0.3
hsv_v: 0.1
degrees: 0.0
translate: 0.02
scale: 0.1
shear: 0.0
perspective: 0.0
flipud: 0.0
fliplr: 0.3
bgr: 0.0
mosaic: 0.3
mixup: 0.0
copy_paste: 0.0
copy_paste_mode: flip
auto_augment: randaugment
erasing: 0.4
crop_fraction: 1.0
cfg: null
tracker: botsort.yaml
save_dir: runs\detect\yolov12n_face_plate_fixed
